# 🚀 UGC Kit Pro - Viral Content Generator

> Create viral UGC content in seconds with our AI-powered toolkit. Transform your content strategy and boost engagement effortlessly.

## ✨ Features

- 🎯 **AI-Powered Script Generation** - Create viral UGC scripts in seconds
- 📱 **Responsive Design** - Works perfectly on all devices
- 🎨 **Modern UI** - Built with shadcn/ui and Tailwind CSS
- 🔒 **Type-Safe** - Full TypeScript support
- ⚡ **Fast Performance** - Optimized with Vite
- 📊 **Analytics Dashboard** - Track your content performance
- 💳 **Payment Integration** - Stripe-ready payment system

## 🛠️ Tech Stack

- **Frontend**: React 18, TypeScript, Vite
- **Styling**: Tailwind CSS, shadcn/ui
- **State Management**: React Query (TanStack Query)
- **Routing**: React Router DOM
- **Forms**: React Hook Form + Zod validation
- **Build Tool**: Vite
- **Package Manager**: npm

## 🚀 Quick Start

### Prerequisites

- Node.js 18.x or higher
- npm

### Installation

1. **Clone the repository**
   ```bash
   git clone <YOUR_GIT_URL>
   cd viral-funnel-flow
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your configuration
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```

## 📁 Project Structure

```
src/
├── components/          # React components
│   ├── ui/             # shadcn/ui components
│   ├── dashboard/      # Dashboard components
│   └── sections/       # Page sections
├── hooks/              # Custom React hooks
├── lib/                # Utility libraries
├── pages/              # Page components
├── types/              # TypeScript definitions
└── utils/              # Helper functions
```

## 🧪 Development

### Available Scripts

```bash
npm run dev              # Start development server
npm run build           # Build for production
npm run preview         # Preview production build
npm run lint            # Run ESLint
npm run lint:fix        # Fix ESLint issues
npm run type-check      # Run TypeScript checks
npm run format          # Format code with Prettier
```

### Code Style

This project uses:
- **ESLint** for code linting
- **Prettier** for code formatting
- **TypeScript** for type safety

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'feat: add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 🙏 Acknowledgments

- [shadcn/ui](https://ui.shadcn.com/) for the beautiful UI components
- [Tailwind CSS](https://tailwindcss.com/) for the utility-first CSS framework
- [Vite](https://vitejs.dev/) for the blazing fast build tool
- [React](https://reactjs.org/) for the amazing frontend library

---

Made with ❤️ by the UGC Kit Pro team
